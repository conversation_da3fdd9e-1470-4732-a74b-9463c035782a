import React from 'react';
import { View, Text, FlatList } from 'react-native';
import { BusinessProduct } from '../../lib/services/businessCardDataService';
import { ProductCard } from '../shared/ui';
import { LoadingSpinner } from '../shared/ui/LoadingSpinner';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';

interface ProductsTabProps {
  products: BusinessProduct[];
  businessId: string;
  isDark: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
}

export default function ProductsTab({
  products,
  businessId,
  isDark,
  loadingMore,
  hasMore,
  onLoadMore,
}: ProductsTabProps) {
  const styles = createPublicCardViewStyles(isDark);

  // Convert BusinessProduct to ProductData for compatibility
  const convertBusinessProductToProductData = (product: BusinessProduct) => {
    return {
      id: product.id,
      name: product.name,
      description: product.description || null,
      base_price: product.base_price || null,
      discounted_price: product.discounted_price || null,
      image_url: product.image_url || null,
      images: product.images || null,
      featured_image_index: product.featured_image_index || null,
      business_id: businessId,
      is_available: true,
      slug: product.slug || `product-${product.id}`,
    };
  };

  return (
    <View style={styles.section}>
      {products && products.length > 0 ? (
        <FlatList
          data={products}
          keyExtractor={(item) => item.id}
          numColumns={2}
          columnWrapperStyle={{ justifyContent: 'space-between' }}
          ItemSeparatorComponent={() => <View style={{ height: 16 }} />}
          onEndReached={onLoadMore}
          onEndReachedThreshold={0.1}
          showsVerticalScrollIndicator={false}
          ListFooterComponent={() =>
            loadingMore ? (
              <View style={{ padding: 20, alignItems: 'center' }}>
                <LoadingSpinner size="small" />
              </View>
            ) : null
          }
          renderItem={({ item }) => (
            <View style={{ flex: 0.48 }}>
              <ProductCard
                product={convertBusinessProductToProductData(item)}
                isClickable={true}
                variant="default"
              />
            </View>
          )}
        />
      ) : (
        <Text style={styles.emptyText}>No products available</Text>
      )}
    </View>
  );
}
