import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  Text,
} from 'react-native';
import {
  Gesture,
  GestureDetector,
  GestureHandlerRootView,
} from 'react-native-gesture-handler';
import { X, ChevronLeft, ChevronRight } from 'lucide-react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';

interface FullScreenImageViewerProps {
  visible: boolean;
  images: { id: string; url: string; caption?: string }[];
  initialIndex: number;
  onClose: () => void;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function FullScreenImageViewer({
  visible,
  images,
  initialIndex,
  onClose,
}: FullScreenImageViewerProps) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);

  // Animation values
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const opacity = useSharedValue(1);

  // Shared values for gesture tracking
  const savedScale = useSharedValue(1);
  const start = useSharedValue({ x: 0, y: 0 });

  // Reset animation values when image changes
  const resetAnimation = useCallback(() => {
    scale.value = withSpring(1);
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    opacity.value = withSpring(1);
    savedScale.value = 1;
    start.value = { x: 0, y: 0 };
  }, [scale, translateX, translateY, opacity, savedScale, start]);

  // Reset current index when modal opens with new initial index
  useEffect(() => {
    if (visible) {
      setCurrentIndex(initialIndex);
      resetAnimation();
    }
  }, [visible, initialIndex, resetAnimation]);

  // Navigate to previous image with infinite scroll
  const goToPrevious = () => {
    // Infinite scroll: go to last image if at the beginning
    const prevIndex = currentIndex > 0 ? currentIndex - 1 : images.length - 1;
    setCurrentIndex(prevIndex);
    resetAnimation();
  };

  // Navigate to next image with infinite scroll
  const goToNext = () => {
    // Infinite scroll: go to first image if at the end
    const nextIndex = currentIndex < images.length - 1 ? currentIndex + 1 : 0;
    setCurrentIndex(nextIndex);
    resetAnimation();
  };

  // Pinch gesture for zoom
  const pinchGesture = Gesture.Pinch()
    .onUpdate((event) => {
      scale.value = savedScale.value * event.scale;
      scale.value = Math.max(0.5, Math.min(3, scale.value));
    })
    .onEnd(() => {
      savedScale.value = scale.value;
      if (scale.value < 1) {
        scale.value = withSpring(1);
        savedScale.value = 1;
      } else if (scale.value > 2.5) {
        scale.value = withSpring(2.5);
        savedScale.value = 2.5;
      }
    });

  // Pan gesture for drag and swipe navigation
  const panGesture = Gesture.Pan()
    .onStart(() => {
      start.value = { x: translateX.value, y: translateY.value };
    })
    .onUpdate((event) => {
      if (scale.value > 1) {
        // If zoomed in, allow panning
        translateX.value = start.value.x + event.translationX;
        translateY.value = start.value.y + event.translationY;
      } else {
        // If not zoomed, allow horizontal swipe for navigation and vertical for close
        translateX.value = event.translationX;
        translateY.value = event.translationY;
        opacity.value = Math.max(0.3, 1 - Math.abs(event.translationY) / 300);
      }
    })
    .onEnd((event) => {
      if (scale.value <= 1) {
        // Check horizontal swipe for navigation (prioritize horizontal over vertical)
        if (Math.abs(event.translationX) > 50 && Math.abs(event.translationX) > Math.abs(event.translationY)) {
          if (event.translationX > 0) {
            // Swipe right - go to previous (infinite scroll)
            runOnJS(goToPrevious)();
          } else if (event.translationX < 0) {
            // Swipe left - go to next (infinite scroll)
            runOnJS(goToNext)();
          }
          translateX.value = withSpring(0);
          translateY.value = withSpring(0);
          opacity.value = withSpring(1);
        }
        // Check if should close modal (vertical swipe)
        else if (Math.abs(event.translationY) > 100 || Math.abs(event.velocityY) > 500) {
          runOnJS(onClose)();
        } else {
          translateX.value = withSpring(0);
          translateY.value = withSpring(0);
          opacity.value = withSpring(1);
        }
      } else {
        // Update start values for next pan when zoomed
        start.value = { x: translateX.value, y: translateY.value };

        // Constrain pan within bounds when zoomed
        const maxTranslateX = (screenWidth * (scale.value - 1)) / 2;
        const maxTranslateY = (screenHeight * (scale.value - 1)) / 2;

        translateX.value = withSpring(
          Math.max(-maxTranslateX, Math.min(maxTranslateX, translateX.value))
        );
        translateY.value = withSpring(
          Math.max(-maxTranslateY, Math.min(maxTranslateY, translateY.value))
        );

        // Update start values after constraining
        start.value = { x: translateX.value, y: translateY.value };
      }
    });

  // Double tap to zoom
  const doubleTapGesture = Gesture.Tap()
    .numberOfTaps(2)
    .onStart(() => {
      if (scale.value > 1) {
        // Zoom out
        scale.value = withSpring(1);
        savedScale.value = 1;
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
        start.value = { x: 0, y: 0 };
      } else {
        // Zoom in
        scale.value = withSpring(2);
        savedScale.value = 2;
      }
    });

  // Combine gestures - use Exclusive for tap vs pan, then Simultaneous with pinch
  const tapAndPanGesture = Gesture.Exclusive(doubleTapGesture, panGesture);
  const composedGesture = Gesture.Simultaneous(pinchGesture, tapAndPanGesture);

  // Animated style for the image
  const animatedImageStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: scale.value },
        { translateX: translateX.value },
        { translateY: translateY.value },
      ],
    };
  });

  // Animated style for the modal background
  const animatedBackgroundStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  // Handle modal close
  const handleClose = () => {
    resetAnimation();
    onClose();
  };

  if (!visible || !images.length) {
    return null;
  }

  const currentImage = images[currentIndex];

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
      statusBarTranslucent
    >
      <GestureHandlerRootView style={{ flex: 1 }}>
        <StatusBar hidden />
        <Animated.View style={[styles.container, animatedBackgroundStyle]}>
        {/* Close button */}
        <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
          <X size={24} color="#FFFFFF" />
        </TouchableOpacity>

        {/* Navigation buttons - always show for infinite scroll */}
        {images.length > 1 && (
          <>
            <TouchableOpacity style={styles.prevButton} onPress={goToPrevious}>
              <ChevronLeft size={32} color="#FFFFFF" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.nextButton} onPress={goToNext}>
              <ChevronRight size={32} color="#FFFFFF" />
            </TouchableOpacity>
          </>
        )}

        {/* Image container */}
        <View style={styles.imageContainer}>
          <GestureDetector gesture={composedGesture}>
            <Animated.View style={styles.gestureContainer}>
              <Animated.Image
                source={{ uri: currentImage.url }}
                style={[styles.image, animatedImageStyle]}
                resizeMode="contain"
              />
            </Animated.View>
          </GestureDetector>
        </View>

        {/* Image counter */}
        {images.length > 1 && (
          <View style={styles.counter}>
            <Text style={styles.counterText}>
              {currentIndex + 1} / {images.length}
            </Text>
          </View>
        )}
        </Animated.View>
      </GestureHandlerRootView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 10,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  prevButton: {
    position: 'absolute',
    left: 20,
    top: '50%',
    marginTop: -22,
    zIndex: 10,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  nextButton: {
    position: 'absolute',
    right: 20,
    top: '50%',
    marginTop: -22,
    zIndex: 10,
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    flex: 1,
    width: screenWidth,
    height: screenHeight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  gestureContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  counter: {
    position: 'absolute',
    bottom: 50,
    alignSelf: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  counterText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
});
