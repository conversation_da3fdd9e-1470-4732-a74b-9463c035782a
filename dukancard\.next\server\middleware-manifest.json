{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_@upstash_redis_b3b75fae._.js", "server/edge/chunks/node_modules_a5b8fa46._.js", "server/edge/chunks/[root-of-the-server]__c2258e89._.js", "server/edge/chunks/edge-wrapper_3918d6b0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "AZMuPbqlbKhnEIjS3+deafwyw7vd5fDFEX7nHsJY2W4=", "__NEXT_PREVIEW_MODE_ID": "20d45c0914fcf06757369dd18b87a043", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "082c2fd5381a01afa8ed4df05bdbffe6c1e264676f00b10093bcacd531538585", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e9749dcb794e1e86218280fcdd7d86b93999c4bd215c7441bdf9cb053aa78e4c"}}}, "sortedMiddleware": ["/"], "functions": {}}